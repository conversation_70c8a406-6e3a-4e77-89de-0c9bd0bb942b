// Script Management System for both incoming and outbound call scripts
// Now uses universal campaign script loader for real scripts from public directory
import { readFile, writeFile, access } from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import universal campaign script loader
import {
    loadCampaignScript,
    getAllCampaigns,
    convertToLegacyFormat,
    getIncomingCallScript as getOutboundCallScript,
    listIncomingCallScripts as listOutboundCallScripts,
    setIncomingCallScript as setOutboundCallScript,
    getCurrentIncomingScript as getCurrentOutboundScript,
    createCustomIncomingScript as createCustomOutboundScript,
    // Incoming scenario functions (now using real campaign scripts)
    getIncomingScenario,
    listIncomingScenarios,
    setActiveIncomingScenario,
    getCurrentIncomingScenario
} from '../../campaign-script-loader.js';

// All incoming scenarios now handled by universal campaign script loader
// No separate incoming system needed - everything uses real campaign scripts

export class ScriptManager {
    constructor() {
        this.currentIncomingScript = 'customer-service';
        this.currentOutboundScript = 'default';
        this.customScripts = new Map();
    }

    // === INCOMING CALL SCRIPTS ===
    
    /**
     * Get all available incoming call scripts
     */
    getIncomingScripts() {
        try {
            const scenarios = listIncomingScenarios();
            return scenarios.map(scenario => ({
                id: scenario.id,
                name: scenario.name,
                description: scenario.description,
                type: 'incoming',
                category: scenario.category || 'support'
            }));
        } catch (error) {
            console.error('Error getting incoming scripts:', error);
            return [];
        }
    }

    /**
     * Get current active incoming script
     */
    getCurrentIncomingScript() {
        try {
            return getCurrentIncomingScenario();
        } catch (error) {
            console.error('Error getting current incoming script:', error);
            return null;
        }
    }

    /**
     * Set active incoming script
     */
    setIncomingScript(scriptId) {
        try {
            return setActiveIncomingScenario(scriptId);
        } catch (error) {
            console.error('Error setting incoming script:', error);
            return false;
        }
    }

    /**
     * Create custom incoming script
     */
    createCustomIncomingScript(scriptData) {
        try {
            // No custom scenarios - we use 6 outbound + 6 inbound ready-made scripts
            console.log('Custom incoming scripts not supported - use campaigns 1-6 (incoming)');
            return false;
        } catch (error) {
            console.error('Error creating custom incoming script:', error);
            return false;
        }
    }

    // === OUTBOUND CALL SCRIPTS ===
    
    /**
     * Get all available outbound call scripts
     */
    getOutboundScripts() {
        try {
            return listOutboundCallScripts();
        } catch (error) {
            console.error('Error getting outbound scripts:', error);
            return [];
        }
    }

    /**
     * Get current active outbound script
     */
    getCurrentOutboundScript() {
        try {
            return getCurrentOutboundScript();
        } catch (error) {
            console.error('Error getting current outbound script:', error);
            return null;
        }
    }

    /**
     * Set active outbound script
     */
    setOutboundScript(scriptId) {
        try {
            return setOutboundCallScript(scriptId);
        } catch (error) {
            console.error('Error setting outbound script:', error);
            return false;
        }
    }

    /**
     * Create custom outbound script
     */
    createCustomOutboundScript(scriptData) {
        try {
            return createCustomOutboundScript(scriptData);
        } catch (error) {
            console.error('Error creating custom outbound script:', error);
            return false;
        }
    }

    // === CAMPAIGN SCRIPT CONVERSION ===
    
    /**
     * Convert legacy incoming scenario to campaign script format
     * @param {Object} incomingScenario - Legacy incoming scenario object
     * @returns {Object} - Campaign script format
     */
    convertIncomingScenarioToCampaignScript(incomingScenario) {
        // Load real incoming campaign script instead of creating placeholder content
        let campaignId = 1; // Default to campaign 1

        // Try to extract campaign ID from scenario ID or name
        if (incomingScenario.id && typeof incomingScenario.id === 'string') {
            const match = incomingScenario.id.match(/(\d+)/);
            if (match) {
                campaignId = parseInt(match[1]);
            }
        }

        // Ensure campaign ID is in valid range (1-6)
        if (campaignId < 1 || campaignId > 6) {
            campaignId = 1;
        }

        console.log(`🔄 [Gemini] Loading real incoming campaign ${campaignId} for scenario: ${incomingScenario.name}`);

        // Load real campaign script from public directory
        const realCampaignScript = loadCampaignScript(campaignId, 'incoming', false);

        if (realCampaignScript) {
            console.log(`✅ [Gemini] Using real incoming campaign script: ${realCampaignScript.title}`);
            return realCampaignScript;
        } else {
            console.warn(`⚠️ [Gemini] Failed to load real campaign ${campaignId}, using minimal fallback`);
            // Minimal fallback - no placeholder content
            return {
                id: campaignId,
                type: 'incoming',
                language: 'en',
                category: 'support',
                title: `Campaign ${campaignId} (Incoming)`,
                campaign: 'Customer Support',
                agentPersona: {
                    name: 'Support Agent',
                    tone: 'Professional, helpful',
                    humanEmulation: true
                },
                script: {
                    start: [
                        { type: 'statement', content: 'Hello, how can I help you today?' }
                    ]
                }
            };
        }
    }

    /**
     * Get script configuration for session
     */
    getScriptConfig(scriptId, isIncoming = false) {
        try {
            // Handle unified ID system: 1-6 = outbound, 7-12 = incoming
            const numericId = parseInt(scriptId);

            if (numericId >= 7 && numericId <= 12) {
                // IDs 7-12 are incoming campaigns (map to incoming-campaign1.json to incoming-campaign6.json)
                const incomingCampaignId = numericId - 6; // 7->1, 8->2, ..., 12->6
                const campaignScript = loadCampaignScript(incomingCampaignId, 'incoming', false);
                if (campaignScript) {
                    return {
                        aiInstructions: `CAMPAIGN SCRIPT - Follow this script exactly:\n\n${JSON.stringify(campaignScript, null, 2)}`,
                        voice: campaignScript.agentPersona?.voice || process.env.GEMINI_DEFAULT_VOICE || 'Kore',
                        model: campaignScript.agentPersona?.model || process.env.GEMINI_DEFAULT_MODEL || 'gemini-2.5-flash-preview-native-audio-dialog',
                        isIncomingCall: true,
                        scriptType: 'incoming',
                        scriptId: scriptId
                    };
                }
            } else if (numericId >= 1 && numericId <= 6) {
                // IDs 1-6 are outbound campaigns (map to campaign1.json to campaign6.json)
                const script = getOutboundCallScript(`campaign-${numericId}`);
                if (script) {
                    return {
                        aiInstructions: script.systemPrompt || script.campaignScript,
                        voice: script.voice || process.env.GEMINI_DEFAULT_VOICE || 'Orus',
                        model: script.model || process.env.GEMINI_DEFAULT_MODEL || 'gemini-2.5-flash-preview-native-audio-dialog',
                        isIncomingCall: false,
                        scriptType: 'outbound',
                        scriptId: scriptId
                    };
                }
            } else if (isIncoming) {
                // Legacy incoming script handling
                const scenario = getIncomingScenario(scriptId);
                if (scenario) {
                    const campaignScript = this.convertIncomingScenarioToCampaignScript(scenario);
                    return {
                        aiInstructions: `CAMPAIGN SCRIPT - Follow this script exactly:\n\n${JSON.stringify(campaignScript, null, 2)}`,
                        voice: scenario.voice || process.env.GEMINI_DEFAULT_VOICE || 'Kore',
                        model: scenario.model || process.env.GEMINI_DEFAULT_MODEL || 'gemini-2.5-flash-preview-native-audio-dialog',
                        isIncomingCall: true,
                        scriptType: 'incoming',
                        scriptId: scriptId
                    };
                }
            } else {
                // Legacy outbound script handling
                const script = getOutboundCallScript(scriptId);
                if (script) {
                    return {
                        aiInstructions: script.systemPrompt || script.campaignScript,
                        voice: script.voice || process.env.GEMINI_DEFAULT_VOICE || 'Orus',
                        model: script.model || process.env.GEMINI_DEFAULT_MODEL || 'gemini-2.5-flash-preview-native-audio-dialog',
                        isIncomingCall: false,
                        scriptType: 'outbound',
                        scriptId: scriptId
                    };
                }
            }
            
            // Fallback to default - no hardcoded instructions
            return {
                aiInstructions: '', // Campaign script should provide all instructions
                voice: process.env.GEMINI_DEFAULT_VOICE || 'Kore',
                model: process.env.GEMINI_DEFAULT_MODEL || 'gemini-2.5-flash-preview-native-audio-dialog',
                isIncomingCall: isIncoming,
                scriptType: isIncoming ? 'incoming' : 'outbound',
                scriptId: 'default'
            };
        } catch (error) {
            console.error('Error getting script config:', error);
            return null;
        }
    }

    /**
     * Validate script data
     */
    validateScript(scriptData) {
        const required = ['id', 'name', 'description'];
        for (const field of required) {
            if (!scriptData[field]) {
                throw new Error(`Missing required field: ${field}`);
            }
        }
        return true;
    }
}

// Export singleton instance
export const scriptManager = new ScriptManager();
